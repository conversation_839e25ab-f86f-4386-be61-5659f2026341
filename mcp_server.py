#!/usr/bin/env python3
"""
简单的MCP数据查询服务器
提供基本的数据查询和分析工具
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from fastmcp import FastMCP, Context

# 创建MCP服务器实例
mcp = FastMCP("数据查询服务器")

# 模拟数据存储
SAMPLE_DATA = {
    "sales": [
        {"date": "2024-01-01", "product": "产品A", "amount": 1000, "quantity": 10},
        {"date": "2024-01-02", "product": "产品B", "amount": 1500, "quantity": 15},
        {"date": "2024-01-03", "product": "产品A", "amount": 800, "quantity": 8},
        {"date": "2024-01-04", "product": "产品C", "amount": 2000, "quantity": 20},
        {"date": "2024-01-05", "product": "产品B", "amount": 1200, "quantity": 12},
    ],
    "users": [
        {"id": 1, "name": "张三", "age": 25, "city": "北京"},
        {"id": 2, "name": "李四", "age": 30, "city": "上海"},
        {"id": 3, "name": "王五", "age": 28, "city": "广州"},
        {"id": 4, "name": "赵六", "age": 35, "city": "深圳"},
    ],
    "products": [
        {"id": "A", "name": "产品A", "price": 100, "category": "电子产品"},
        {"id": "B", "name": "产品B", "price": 100, "category": "家居用品"},
        {"id": "C", "name": "产品C", "price": 100, "category": "电子产品"},
    ]
}

@mcp.tool
async def get_sales_data(date_from: Optional[str] = None, date_to: Optional[str] = None) -> Dict[str, Any]:
    """
    获取销售数据
    
    Args:
        date_from: 开始日期 (YYYY-MM-DD格式，可选)
        date_to: 结束日期 (YYYY-MM-DD格式，可选)
    
    Returns:
        销售数据列表
    """
    sales_data = SAMPLE_DATA["sales"].copy()
    
    if date_from:
        sales_data = [item for item in sales_data if item["date"] >= date_from]
    
    if date_to:
        sales_data = [item for item in sales_data if item["date"] <= date_to]
    
    return {
        "data": sales_data,
        "total_records": len(sales_data),
        "total_amount": sum(item["amount"] for item in sales_data),
        "total_quantity": sum(item["quantity"] for item in sales_data)
    }

@mcp.tool
async def get_user_data(city: Optional[str] = None) -> Dict[str, Any]:
    """
    获取用户数据
    
    Args:
        city: 城市筛选条件 (可选)
    
    Returns:
        用户数据列表
    """
    user_data = SAMPLE_DATA["users"].copy()
    
    if city:
        user_data = [user for user in user_data if user["city"] == city]
    
    return {
        "data": user_data,
        "total_users": len(user_data),
        "average_age": sum(user["age"] for user in user_data) / len(user_data) if user_data else 0
    }

@mcp.tool
async def get_product_data(category: Optional[str] = None) -> Dict[str, Any]:
    """
    获取产品数据
    
    Args:
        category: 产品类别筛选条件 (可选)
    
    Returns:
        产品数据列表
    """
    product_data = SAMPLE_DATA["products"].copy()
    
    if category:
        product_data = [product for product in product_data if product["category"] == category]
    
    return {
        "data": product_data,
        "total_products": len(product_data),
        "average_price": sum(product["price"] for product in product_data) / len(product_data) if product_data else 0
    }

@mcp.tool
async def analyze_sales_by_product() -> Dict[str, Any]:
    """
    按产品分析销售数据
    
    Returns:
        按产品分组的销售分析结果
    """
    sales_data = SAMPLE_DATA["sales"]
    
    # 使用pandas进行数据分析
    df = pd.DataFrame(sales_data)
    
    analysis = df.groupby('product').agg({
        'amount': ['sum', 'mean', 'count'],
        'quantity': ['sum', 'mean']
    }).round(2)
    
    # 转换为字典格式
    result = {}
    for product in analysis.index:
        result[product] = {
            "total_amount": float(analysis.loc[product, ('amount', 'sum')]),
            "average_amount": float(analysis.loc[product, ('amount', 'mean')]),
            "order_count": int(analysis.loc[product, ('amount', 'count')]),
            "total_quantity": int(analysis.loc[product, ('quantity', 'sum')]),
            "average_quantity": float(analysis.loc[product, ('quantity', 'mean')])
        }
    
    return {
        "analysis": result,
        "summary": {
            "total_products": len(result),
            "best_selling_product": max(result.keys(), key=lambda x: result[x]["total_amount"]),
            "total_revenue": sum(item["total_amount"] for item in result.values())
        }
    }

@mcp.tool
async def get_data_summary() -> Dict[str, Any]:
    """
    获取所有数据的汇总信息
    
    Returns:
        数据汇总统计
    """
    return {
        "sales": {
            "total_records": len(SAMPLE_DATA["sales"]),
            "date_range": {
                "from": min(item["date"] for item in SAMPLE_DATA["sales"]),
                "to": max(item["date"] for item in SAMPLE_DATA["sales"])
            }
        },
        "users": {
            "total_users": len(SAMPLE_DATA["users"]),
            "cities": list(set(user["city"] for user in SAMPLE_DATA["users"]))
        },
        "products": {
            "total_products": len(SAMPLE_DATA["products"]),
            "categories": list(set(product["category"] for product in SAMPLE_DATA["products"]))
        }
    }

@mcp.resource("data://sales")
async def get_sales_resource() -> str:
    """提供销售数据资源"""
    return json.dumps(SAMPLE_DATA["sales"], ensure_ascii=False, indent=2)

@mcp.resource("data://users")
async def get_users_resource() -> str:
    """提供用户数据资源"""
    return json.dumps(SAMPLE_DATA["users"], ensure_ascii=False, indent=2)

@mcp.resource("data://products")
async def get_products_resource() -> str:
    """提供产品数据资源"""
    return json.dumps(SAMPLE_DATA["products"], ensure_ascii=False, indent=2)

if __name__ == "__main__":
    # 运行MCP服务器
    print("启动数据查询MCP服务器...")
    mcp.run()
