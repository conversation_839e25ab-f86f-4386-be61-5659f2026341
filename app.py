#!/usr/bin/env python3
"""
智能数据查询助手 - Streamlit前端界面
基于ReAct Agent的智能数据查询系统
"""

import streamlit as st
import asyncio
import os
from datetime import datetime
from dotenv import load_dotenv
from react_agent import DataQueryAgent

# 加载环境变量
load_dotenv()

# 页面配置
st.set_page_config(
    page_title="智能数据查询助手",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #1f77b4;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left-color: #2196f3;
    }
    .assistant-message {
        background-color: #f5f5f5;
        border-left-color: #4caf50;
    }
    .sidebar-info {
        background-color: #f0f8ff;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if "messages" not in st.session_state:
    st.session_state.messages = []

if "agent" not in st.session_state:
    st.session_state.agent = None

if "agent_initialized" not in st.session_state:
    st.session_state.agent_initialized = False

async def initialize_agent():
    """初始化Agent"""
    if not st.session_state.agent_initialized:
        with st.spinner("正在初始化智能助手..."):
            try:
                agent = DataQueryAgent()
                await agent.setup_mcp_client()
                agent.create_agent()
                st.session_state.agent = agent
                st.session_state.agent_initialized = True
                st.success("✅ 智能助手初始化成功！")
                return True
            except Exception as e:
                st.error(f"❌ 智能助手初始化失败: {str(e)}")
                return False
    return True

def display_chat_message(role: str, content: str):
    """显示聊天消息"""
    if role == "user":
        st.markdown(f"""
        <div class="chat-message user-message">
            <strong>👤 您:</strong><br>
            {content}
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="chat-message assistant-message">
            <strong>🤖 智能助手:</strong><br>
            {content}
        </div>
        """, unsafe_allow_html=True)

async def get_agent_response(question: str) -> str:
    """获取Agent回答"""
    if st.session_state.agent:
        try:
            response = await st.session_state.agent.query(question, thread_id="streamlit_session")
            return response
        except Exception as e:
            return f"抱歉，处理您的问题时出现错误: {str(e)}"
    else:
        return "智能助手尚未初始化，请稍后再试。"

def main():
    """主函数"""
    # 页面标题
    st.markdown('<h1 class="main-header">🤖 智能数据查询助手</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 📊 系统信息")
        
        # 系统状态
        st.markdown('<div class="sidebar-info">', unsafe_allow_html=True)
        st.markdown("**🔧 系统配置:**")
        st.markdown(f"- 模型: {os.getenv('MODEL_NAME', '未配置')}")
        st.markdown(f"- 提供商: {os.getenv('MODEL_PROVIDER', '未配置')}")
        st.markdown(f"- 状态: {'✅ 已连接' if st.session_state.agent_initialized else '⏳ 初始化中'}")
        st.markdown('</div>', unsafe_allow_html=True)
        
        # 功能说明
        st.markdown("### 🛠️ 可用功能")
        st.markdown("""
        - **销售数据查询**: 查询销售记录和统计
        - **用户数据查询**: 查询用户信息和分析
        - **产品销售分析**: 按产品分析销售情况
        - **数据汇总**: 获取完整数据概览
        """)
        
        # 示例问题
        st.markdown("### 💡 示例问题")
        example_questions = [
            "查询销售数据概况",
            "分析各产品销售情况", 
            "北京有多少用户？",
            "给我一个数据汇总",
            "哪个产品销售最好？"
        ]
        
        for i, question in enumerate(example_questions):
            if st.button(f"📝 {question}", key=f"example_{i}"):
                st.session_state.current_question = question
        
        # 清空对话按钮
        if st.button("🗑️ 清空对话历史"):
            st.session_state.messages = []
            st.rerun()
    
    # 主内容区域
    col1, col2 = st.columns([3, 1])
    
    with col1:
        # 初始化Agent
        if not st.session_state.agent_initialized:
            if st.button("🚀 启动智能助手", type="primary"):
                if asyncio.run(initialize_agent()):
                    st.rerun()
        
        # 显示对话历史
        if st.session_state.messages:
            st.markdown("### 💬 对话历史")
            for message in st.session_state.messages:
                display_chat_message(message["role"], message["content"])
        
        # 输入区域
        if st.session_state.agent_initialized:
            st.markdown("### 🔍 提出您的问题")
            
            # 检查是否有示例问题被点击
            question = ""
            if hasattr(st.session_state, 'current_question'):
                question = st.session_state.current_question
                delattr(st.session_state, 'current_question')
            
            user_input = st.text_area(
                "请输入您的问题:",
                value=question,
                height=100,
                placeholder="例如：查询销售数据概况、分析产品销售情况、北京有多少用户等..."
            )
            
            col_btn1, col_btn2 = st.columns([1, 4])
            with col_btn1:
                if st.button("📤 发送", type="primary"):
                    if user_input.strip():
                        # 添加用户消息
                        st.session_state.messages.append({
                            "role": "user", 
                            "content": user_input,
                            "timestamp": datetime.now()
                        })
                        
                        # 显示用户消息
                        display_chat_message("user", user_input)
                        
                        # 获取AI回答
                        with st.spinner("🤔 智能助手正在思考..."):
                            try:
                                response = asyncio.run(get_agent_response(user_input))
                                
                                # 添加AI消息
                                st.session_state.messages.append({
                                    "role": "assistant",
                                    "content": response,
                                    "timestamp": datetime.now()
                                })
                                
                                # 显示AI回答
                                display_chat_message("assistant", response)
                                
                            except Exception as e:
                                error_msg = f"抱歉，处理您的问题时出现错误: {str(e)}"
                                st.error(error_msg)
                                st.session_state.messages.append({
                                    "role": "assistant",
                                    "content": error_msg,
                                    "timestamp": datetime.now()
                                })
                        
                        st.rerun()
                    else:
                        st.warning("请输入您的问题！")
        else:
            st.info("👆 请先点击上方按钮启动智能助手")
    
    with col2:
        # 统计信息
        st.markdown("### 📈 会话统计")
        st.metric("对话轮数", len(st.session_state.messages) // 2)
        st.metric("用户提问", len([m for m in st.session_state.messages if m["role"] == "user"]))
        st.metric("助手回答", len([m for m in st.session_state.messages if m["role"] == "assistant"]))
        
        # 最近活动
        if st.session_state.messages:
            st.markdown("### ⏰ 最近活动")
            recent_messages = st.session_state.messages[-3:]
            for msg in recent_messages:
                timestamp = msg.get("timestamp", datetime.now())
                role_icon = "👤" if msg["role"] == "user" else "🤖"
                st.markdown(f"{role_icon} {timestamp.strftime('%H:%M:%S')}")
                st.markdown(f"_{msg['content'][:50]}..._" if len(msg['content']) > 50 else f"_{msg['content']}_")
                st.markdown("---")

if __name__ == "__main__":
    main()
