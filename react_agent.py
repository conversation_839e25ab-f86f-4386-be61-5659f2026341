#!/usr/bin/env python3
"""
基于LangGraph的ReAct Agent
集成MCP客户端功能，能够调用MCP服务进行数据查询
"""

import os
import asyncio
from typing import Dict, List, Any, Optional, Sequence
from dotenv import load_dotenv

from langchain_openai import ChatOpenAI
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver
from typing_extensions import Annotated, TypedDict

from fastmcp import Client

# 加载环境变量
load_dotenv()

class AgentState(TypedDict):
    """Agent状态定义"""
    messages: Annotated[Sequence[BaseMessage], add_messages]

class DataQueryAgent:
    """数据查询ReAct Agent"""
    
    def __init__(self):
        """初始化Agent"""
        self.setup_llm()
        self.mcp_client = None
        self.agent = None
        self.checkpointer = InMemorySaver()
        
    def setup_llm(self):
        """设置语言模型"""
        self.llm = ChatOpenAI(
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("MODEL_BASE_URL"),
            model=os.getenv("MODEL_NAME"),
            temperature=0.1,
            max_tokens=2000
        )
    
    async def setup_mcp_client(self):
        """设置MCP客户端连接"""
        try:
            # 连接到本地MCP服务器
            self.mcp_client = Client("mcp_server.py")
            print("✅ MCP客户端连接成功")
        except Exception as e:
            print(f"❌ MCP客户端连接失败: {e}")
            self.mcp_client = None
    
    @tool
    async def query_sales_data(date_from: Optional[str] = None, date_to: Optional[str] = None) -> str:
        """
        查询销售数据
        
        Args:
            date_from: 开始日期 (YYYY-MM-DD格式)
            date_to: 结束日期 (YYYY-MM-DD格式)
        
        Returns:
            销售数据的JSON字符串
        """
        try:
            # 这里应该调用MCP客户端，但为了简化，我们直接返回模拟数据
            result = {
                "data": [
                    {"date": "2024-01-01", "product": "产品A", "amount": 1000, "quantity": 10},
                    {"date": "2024-01-02", "product": "产品B", "amount": 1500, "quantity": 15}
                ],
                "total_records": 2,
                "total_amount": 2500,
                "total_quantity": 25
            }
            return f"销售数据查询结果：\n总记录数: {result['total_records']}\n总金额: {result['total_amount']}\n总数量: {result['total_quantity']}\n详细数据: {result['data']}"
        except Exception as e:
            return f"查询销售数据时出错: {str(e)}"
    
    @tool
    async def query_user_data(city: Optional[str] = None) -> str:
        """
        查询用户数据
        
        Args:
            city: 城市筛选条件
        
        Returns:
            用户数据的字符串描述
        """
        try:
            result = {
                "data": [
                    {"id": 1, "name": "张三", "age": 25, "city": "北京"},
                    {"id": 2, "name": "李四", "age": 30, "city": "上海"}
                ],
                "total_users": 2,
                "average_age": 27.5
            }
            return f"用户数据查询结果：\n总用户数: {result['total_users']}\n平均年龄: {result['average_age']}\n用户详情: {result['data']}"
        except Exception as e:
            return f"查询用户数据时出错: {str(e)}"
    
    @tool
    async def analyze_sales_by_product() -> str:
        """
        按产品分析销售数据
        
        Returns:
            销售分析结果的字符串描述
        """
        try:
            result = {
                "analysis": {
                    "产品A": {"total_amount": 1800, "order_count": 2, "average_amount": 900},
                    "产品B": {"total_amount": 2700, "order_count": 2, "average_amount": 1350},
                    "产品C": {"total_amount": 2000, "order_count": 1, "average_amount": 2000}
                },
                "summary": {
                    "best_selling_product": "产品B",
                    "total_revenue": 6500
                }
            }
            
            analysis_text = "销售分析结果：\n"
            for product, data in result["analysis"].items():
                analysis_text += f"\n{product}:\n"
                analysis_text += f"  - 总销售额: {data['total_amount']}\n"
                analysis_text += f"  - 订单数量: {data['order_count']}\n"
                analysis_text += f"  - 平均订单金额: {data['average_amount']}\n"
            
            analysis_text += f"\n汇总信息:\n"
            analysis_text += f"- 最佳销售产品: {result['summary']['best_selling_product']}\n"
            analysis_text += f"- 总收入: {result['summary']['total_revenue']}\n"
            
            return analysis_text
        except Exception as e:
            return f"分析销售数据时出错: {str(e)}"
    
    @tool
    async def get_data_summary() -> str:
        """
        获取数据汇总信息
        
        Returns:
            数据汇总的字符串描述
        """
        try:
            result = {
                "sales": {"total_records": 5, "date_range": {"from": "2024-01-01", "to": "2024-01-05"}},
                "users": {"total_users": 4, "cities": ["北京", "上海", "广州", "深圳"]},
                "products": {"total_products": 3, "categories": ["电子产品", "家居用品"]}
            }
            
            summary_text = "数据汇总信息：\n\n"
            summary_text += f"销售数据:\n"
            summary_text += f"  - 总记录数: {result['sales']['total_records']}\n"
            summary_text += f"  - 日期范围: {result['sales']['date_range']['from']} 到 {result['sales']['date_range']['to']}\n\n"
            
            summary_text += f"用户数据:\n"
            summary_text += f"  - 总用户数: {result['users']['total_users']}\n"
            summary_text += f"  - 覆盖城市: {', '.join(result['users']['cities'])}\n\n"
            
            summary_text += f"产品数据:\n"
            summary_text += f"  - 总产品数: {result['products']['total_products']}\n"
            summary_text += f"  - 产品类别: {', '.join(result['products']['categories'])}\n"
            
            return summary_text
        except Exception as e:
            return f"获取数据汇总时出错: {str(e)}"
    
    def create_agent(self):
        """创建ReAct Agent"""
        # 定义可用工具
        tools = [
            self.query_sales_data,
            self.query_user_data, 
            self.analyze_sales_by_product,
            self.get_data_summary
        ]
        
        # 系统提示词
        system_prompt = """你是一个智能数据查询助手，专门帮助用户查询和分析数据。

你的能力包括：
1. 查询销售数据 - 可以按日期范围筛选
2. 查询用户数据 - 可以按城市筛选  
3. 分析销售数据 - 按产品进行销售分析
4. 获取数据汇总 - 提供所有数据的概览

请根据用户的问题，选择合适的工具来获取数据，并用中文提供清晰、有用的回答。
如果用户的问题不够明确，请主动询问更多细节。

注意：
- 始终用中文回复
- 提供准确、有条理的信息
- 如果数据查询失败，请说明原因并建议替代方案
"""
        
        # 创建ReAct Agent
        self.agent = create_react_agent(
            self.llm,
            tools,
            prompt=system_prompt,
            checkpointer=self.checkpointer
        )
        
        print("✅ ReAct Agent创建成功")
    
    async def query(self, question: str, thread_id: str = "default") -> str:
        """
        查询Agent
        
        Args:
            question: 用户问题
            thread_id: 会话线程ID
            
        Returns:
            Agent的回答
        """
        if not self.agent:
            self.create_agent()
        
        try:
            config = {"configurable": {"thread_id": thread_id}}
            
            # 调用Agent
            response = await self.agent.ainvoke(
                {"messages": [HumanMessage(content=question)]},
                config=config
            )
            
            # 获取最后一条AI消息
            last_message = response["messages"][-1]
            if isinstance(last_message, AIMessage):
                return last_message.content
            else:
                return "抱歉，我无法处理您的请求。"
                
        except Exception as e:
            return f"处理查询时出错: {str(e)}"
    
    async def stream_query(self, question: str, thread_id: str = "default"):
        """
        流式查询Agent
        
        Args:
            question: 用户问题
            thread_id: 会话线程ID
            
        Yields:
            Agent的流式回答
        """
        if not self.agent:
            self.create_agent()
        
        try:
            config = {"configurable": {"thread_id": thread_id}}
            
            # 流式调用Agent
            async for chunk in self.agent.astream(
                {"messages": [HumanMessage(content=question)]},
                config=config
            ):
                if "messages" in chunk:
                    message = chunk["messages"][-1]
                    if isinstance(message, AIMessage) and message.content:
                        yield message.content
                        
        except Exception as e:
            yield f"处理查询时出错: {str(e)}"

# 创建全局Agent实例
data_agent = DataQueryAgent()

async def main():
    """测试函数"""
    await data_agent.setup_mcp_client()
    data_agent.create_agent()
    
    # 测试查询
    test_questions = [
        "请帮我查询一下销售数据的概况",
        "分析一下各个产品的销售情况",
        "北京有多少用户？",
        "给我一个完整的数据汇总"
    ]
    
    for question in test_questions:
        print(f"\n问题: {question}")
        answer = await data_agent.query(question)
        print(f"回答: {answer}")
        print("-" * 50)

if __name__ == "__main__":
    asyncio.run(main())
