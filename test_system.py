#!/usr/bin/env python3
"""
系统测试脚本
测试MCP服务器、ReAct Agent和整体系统功能
"""

import asyncio
import sys
import os
from dotenv import load_dotenv
from fastmcp import Client
from react_agent import DataQueryAgent

# 加载环境变量
load_dotenv()

class SystemTester:
    """系统测试器"""
    
    def __init__(self):
        self.mcp_client = None
        self.agent = None
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append({
            "name": test_name,
            "success": success,
            "message": message
        })
    
    async def test_environment_variables(self):
        """测试环境变量配置"""
        print("\n🔧 测试环境变量配置...")
        
        required_vars = [
            "OPENAI_API_KEY",
            "MODEL_BASE_URL", 
            "MODEL_NAME",
            "MODEL_PROVIDER"
        ]
        
        all_present = True
        for var in required_vars:
            value = os.getenv(var)
            if value:
                self.log_test(f"环境变量 {var}", True, f"已配置")
            else:
                self.log_test(f"环境变量 {var}", False, "未配置")
                all_present = False
        
        return all_present
    
    async def test_mcp_server(self):
        """测试MCP服务器功能"""
        print("\n📡 测试MCP服务器...")
        
        try:
            # 测试MCP服务器导入
            from mcp_server import mcp, SAMPLE_DATA
            self.log_test("MCP服务器模块导入", True)
            
            # 测试数据完整性
            required_data = ["sales", "users", "products"]
            for data_type in required_data:
                if data_type in SAMPLE_DATA and SAMPLE_DATA[data_type]:
                    self.log_test(f"样本数据 {data_type}", True, f"包含 {len(SAMPLE_DATA[data_type])} 条记录")
                else:
                    self.log_test(f"样本数据 {data_type}", False, "数据缺失")
            
            return True
            
        except Exception as e:
            self.log_test("MCP服务器测试", False, str(e))
            return False
    
    async def test_react_agent(self):
        """测试ReAct Agent功能"""
        print("\n🤖 测试ReAct Agent...")
        
        try:
            # 创建Agent实例
            self.agent = DataQueryAgent()
            self.log_test("Agent实例创建", True)
            
            # 测试LLM配置
            if self.agent.llm:
                self.log_test("LLM配置", True)
            else:
                self.log_test("LLM配置", False, "LLM未正确配置")
                return False
            
            # 创建Agent
            self.agent.create_agent()
            if self.agent.agent:
                self.log_test("Agent创建", True)
            else:
                self.log_test("Agent创建", False)
                return False
            
            # 测试基本查询功能
            test_question = "请给我一个数据汇总"
            try:
                response = await self.agent.query(test_question, thread_id="test_session")
                if response and len(response) > 10:
                    self.log_test("Agent查询功能", True, f"回答长度: {len(response)} 字符")
                else:
                    self.log_test("Agent查询功能", False, "回答过短或为空")
            except Exception as e:
                self.log_test("Agent查询功能", False, str(e))
            
            return True
            
        except Exception as e:
            self.log_test("ReAct Agent测试", False, str(e))
            return False
    
    async def test_agent_tools(self):
        """测试Agent工具功能"""
        print("\n🛠️ 测试Agent工具...")
        
        if not self.agent:
            self.log_test("工具测试", False, "Agent未初始化")
            return False
        
        # 测试各个工具
        test_cases = [
            ("销售数据查询", "查询销售数据"),
            ("用户数据查询", "查询用户数据"),
            ("销售分析", "分析各产品的销售情况"),
            ("数据汇总", "给我一个完整的数据汇总")
        ]
        
        success_count = 0
        for tool_name, question in test_cases:
            try:
                response = await self.agent.query(question, thread_id="tool_test")
                if response and "出错" not in response:
                    self.log_test(f"工具: {tool_name}", True)
                    success_count += 1
                else:
                    self.log_test(f"工具: {tool_name}", False, "返回错误信息")
            except Exception as e:
                self.log_test(f"工具: {tool_name}", False, str(e))
        
        return success_count == len(test_cases)
    
    async def test_streamlit_dependencies(self):
        """测试Streamlit相关依赖"""
        print("\n🌐 测试Streamlit依赖...")
        
        try:
            import streamlit as st
            self.log_test("Streamlit导入", True)
            
            # 测试其他关键依赖
            dependencies = [
                ("pandas", "pandas"),
                ("numpy", "numpy"), 
                ("dotenv", "python-dotenv"),
                ("langchain_openai", "langchain-openai"),
                ("langgraph", "langgraph"),
                ("fastmcp", "fastmcp")
            ]
            
            for module_name, package_name in dependencies:
                try:
                    __import__(module_name)
                    self.log_test(f"依赖 {package_name}", True)
                except ImportError:
                    self.log_test(f"依赖 {package_name}", False, "导入失败")
            
            return True
            
        except Exception as e:
            self.log_test("Streamlit依赖测试", False, str(e))
            return False
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 测试报告汇总")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['name']}: {result['message']}")
        
        print("\n" + "="*60)
        
        return failed_tests == 0
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始系统测试...")
        
        # 运行各项测试
        await self.test_environment_variables()
        await self.test_mcp_server()
        await self.test_react_agent()
        await self.test_agent_tools()
        await self.test_streamlit_dependencies()
        
        # 生成报告
        success = self.generate_report()
        
        if success:
            print("\n🎉 所有测试通过！系统准备就绪。")
            print("\n📝 启动说明:")
            print("1. 运行 Streamlit 应用: streamlit run app.py")
            print("2. 在浏览器中访问显示的URL")
            print("3. 点击'启动智能助手'按钮")
            print("4. 开始与智能助手对话")
        else:
            print("\n⚠️ 部分测试失败，请检查上述错误信息。")
        
        return success

async def main():
    """主函数"""
    tester = SystemTester()
    success = await tester.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
