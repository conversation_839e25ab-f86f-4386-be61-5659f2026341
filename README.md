# 智能数据查询助手

基于ReAct Agent的智能数据查询系统，集成了LangGraph、FastMCP和Streamlit技术栈。

## 🌟 功能特性

- **🤖 智能对话**: 基于ReAct Agent的自然语言数据查询
- **📊 数据分析**: 支持销售数据、用户数据、产品数据的查询和分析
- **🔧 MCP集成**: 使用Model Context Protocol进行数据服务通信
- **🌐 Web界面**: 美观的中文Streamlit前端界面
- **💬 对话记忆**: 支持多轮对话和上下文理解

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │   ReAct Agent   │    │   MCP Server    │
│   前端界面      │◄──►│   (LangGraph)   │◄──►│   (FastMCP)     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 技术栈

- **前端**: Streamlit 1.49+
- **AI框架**: LangGraph 0.6+, LangChain 0.3+
- **模型**: 通过ModelScope API调用Qwen3-Coder-480B
- **MCP**: FastMCP 2.12+
- **数据处理**: Pandas, NumPy

## 🚀 快速开始

### 1. 环境配置

确保已安装Python 3.11+和uv包管理器。

```bash
# 安装依赖
uv sync
```

### 2. 配置环境变量

项目已包含`.env`文件，配置了以下参数：

```env
OPENAI_API_KEY=ms-b2129be5-2245-4c65-9a00-b7a4f4eeaa10
MODEL_BASE_URL=https://api-inference.modelscope.cn/v1/
MODEL_NAME=Qwen/Qwen3-Coder-480B-A35B-Instruct
MODEL_PROVIDER=ModelScope
```

### 3. 运行系统测试

```bash
python test_system.py
```

### 4. 启动应用

```bash
streamlit run app.py
```

应用将在浏览器中自动打开，通常地址为 `http://localhost:8501`

## 🎯 使用指南

### 启动智能助手

1. 打开Web界面后，点击"🚀 启动智能助手"按钮
2. 等待系统初始化完成
3. 在输入框中输入您的问题

### 示例查询

- **数据概览**: "给我一个完整的数据汇总"
- **销售分析**: "分析各个产品的销售情况"
- **用户查询**: "北京有多少用户？"
- **销售数据**: "查询最近的销售数据"

### 可用功能

- 📈 **销售数据查询**: 按日期范围查询销售记录
- 👥 **用户数据查询**: 按城市筛选用户信息
- 📊 **产品销售分析**: 分析各产品销售表现
- 📋 **数据汇总**: 获取完整数据概览

## 📁 项目结构

```
data_analysis/
├── .env                 # 环境变量配置
├── pyproject.toml       # 项目依赖配置
├── app.py              # Streamlit前端应用
├── react_agent.py      # ReAct Agent实现
├── mcp_server.py       # MCP数据服务器
├── test_system.py      # 系统测试脚本
└── README.md           # 项目文档
```

## 🔧 核心组件

### ReAct Agent (`react_agent.py`)
- 基于LangGraph构建的智能代理
- 集成多个数据查询工具
- 支持自然语言理解和推理

### MCP服务器 (`mcp_server.py`)
- 使用FastMCP框架构建
- 提供数据查询和分析API
- 包含模拟的销售、用户、产品数据

### Streamlit界面 (`app.py`)
- 响应式中文Web界面
- 实时对话功能
- 会话历史管理

## 🧪 测试

运行完整的系统测试：

```bash
python test_system.py
```

测试覆盖：
- ✅ 环境变量配置
- ✅ MCP服务器功能
- ✅ ReAct Agent创建和查询
- ✅ Agent工具功能
- ✅ Streamlit依赖

## 🎨 界面特性

- 🎯 **直观设计**: 清晰的对话界面
- 📱 **响应式布局**: 适配不同屏幕尺寸
- 🎨 **美观样式**: 自定义CSS样式
- 📊 **实时统计**: 会话统计和活动记录
- 💡 **示例问题**: 预设常用查询示例

## 🔍 故障排除

### 常见问题

1. **模型连接失败**
   - 检查网络连接
   - 验证API密钥和端点配置

2. **依赖安装问题**
   - 确保使用Python 3.11+
   - 运行 `uv sync` 重新安装依赖

3. **Agent初始化失败**
   - 检查环境变量配置
   - 运行测试脚本诊断问题

## 📈 性能优化

- 使用内存检查点保存对话状态
- 异步处理提升响应速度
- 流式输出改善用户体验

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目采用MIT许可证。